import type { APIRoute } from "astro";
import { v4 as uuidv4 } from "uuid";
export const prerender = false;

export const POST: APIRoute = async ({ request, locals }) => {
  // @ts-ignore - Cloudflare env bindings are injected at runtime
  const env = (locals as any).runtime.env;
  // @ts-ignore
  const bucket = env.QR_LOGOS as R2Bucket;

  if (!bucket) {
    return new Response(JSON.stringify({ error: "R2 bucket not configured." }), { status: 500 });
  }

  const formData = await request.formData();
  const file = formData.get("file");
  if (!file || !(file instanceof File)) {
    return new Response(JSON.stringify({ error: "No file provided" }), { status: 400 });
  }

  const ext = file.type.split("/").pop() || "png";
  const key = `logos/${Date.now()}-${uuidv4()}.${ext}`;

  // Convert file to ArrayBuffer to provide known length for R2
  const fileBuffer = await file.arrayBuffer();

  await bucket.put(key, fileBuffer, {
    httpMetadata: { contentType: file.type },
  });

  // Public URL – assumes bucket is bound to a public R2 custom domain.
  // @ts-ignore
  const publicHost = `cdn.qranalytica.com`;
  const url = `https://${publicHost}/${key}`;

  return new Response(JSON.stringify({ url }), { headers: { "Content-Type": "application/json" }, status: 201 });
}; 