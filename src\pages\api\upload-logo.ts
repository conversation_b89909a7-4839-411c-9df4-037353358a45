import type { APIRoute } from "astro";
import { v4 as uuidv4 } from "uuid";
export const prerender = false;

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    console.log("Upload logo API called");

    // @ts-ignore - Cloudflare env bindings are injected at runtime
    const env = (locals as any).runtime?.env;
    console.log("Environment available:", !!env);

    // Check if we're in development mode (no env available)
    const isDevelopment = !env;

    if (isDevelopment) {
      console.log("Development mode detected - simulating upload");

      const formData = await request.formData();
      const file = formData.get("file");

      if (!file || !(file instanceof File)) {
        console.error("No valid file provided");
        return new Response(JSON.stringify({ error: "No file provided" }), { status: 400 });
      }

      console.log("File details:", {
        name: file.name,
        type: file.type,
        size: file.size
      });

      // In development, return a placeholder URL
      const ext = file.type.split("/").pop() || "png";
      const mockKey = `logos/dev-${Date.now()}-${uuidv4()}.${ext}`;
      const mockUrl = `https://cdn.qranalytica.com/${mockKey}`;

      console.log("Development mode - returning mock URL:", mockUrl);

      return new Response(JSON.stringify({
        url: mockUrl,
        development: true,
        message: "Development mode - file not actually uploaded"
      }), {
        headers: { "Content-Type": "application/json" },
        status: 201
      });
    }

    // Production mode with R2
    // @ts-ignore
    const bucket = env.QR_LOGOS as R2Bucket;
    console.log("Bucket available:", !!bucket);

    if (!bucket) {
      console.error("R2 bucket not configured");
      return new Response(JSON.stringify({ error: "R2 bucket not configured." }), { status: 500 });
    }

    const formData = await request.formData();
    const file = formData.get("file");
    console.log("File received:", !!file, file?.constructor.name);

    if (!file || !(file instanceof File)) {
      console.error("No valid file provided");
      return new Response(JSON.stringify({ error: "No file provided" }), { status: 400 });
    }

    console.log("File details:", {
      name: file.name,
      type: file.type,
      size: file.size
    });

    const ext = file.type.split("/").pop() || "png";
    const key = `logos/${Date.now()}-${uuidv4()}.${ext}`;
    console.log("Generated key:", key);

    // Convert file to ArrayBuffer to provide known length for R2
    const fileBuffer = await file.arrayBuffer();
    console.log("File buffer size:", fileBuffer.byteLength);

    console.log("Attempting to upload to R2...");
    const result = await bucket.put(key, fileBuffer, {
      httpMetadata: { contentType: file.type },
    });
    console.log("Upload result:", !!result);

    // Public URL – assumes bucket is bound to a public R2 custom domain.
    const publicHost = `cdn.qranalytica.com`;
    const url = `https://${publicHost}/${key}`;
    console.log("Generated URL:", url);

    return new Response(JSON.stringify({ url }), { headers: { "Content-Type": "application/json" }, status: 201 });
  } catch (error) {
    console.error("Upload error:", error);
    return new Response(JSON.stringify({
      error: "Upload failed",
      details: error instanceof Error ? error.message : String(error)
    }), { status: 500 });
  }
};